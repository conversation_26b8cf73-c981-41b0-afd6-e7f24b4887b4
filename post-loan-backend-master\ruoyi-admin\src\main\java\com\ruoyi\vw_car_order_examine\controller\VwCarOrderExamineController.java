package com.ruoyi.vw_car_order_examine.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.vw_car_order_examine.domain.VwCarOrderExamine;
import com.ruoyi.vw_car_order_examine.service.IVwCarOrderExamineService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 找车费用审批Controller
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@RestController
@RequestMapping("/vw_car_order_examine/vw_car_order_examine")
public class VwCarOrderExamineController extends BaseController
{
    @Autowired
    private IVwCarOrderExamineService vwCarOrderExamineService;

    /**
     * 查询找车费用审批列表
     */
    @PreAuthorize("@ss.hasPermi('vw_car_order_examine:vw_car_order_examine:list')")
    @GetMapping("/list")
    public TableDataInfo list(VwCarOrderExamine vwCarOrderExamine)
    {
        startPage();
        List<VwCarOrderExamine> list = vwCarOrderExamineService.selectVwCarOrderExamineList(vwCarOrderExamine);
        return getDataTable(list);
    }

    /**
     * 导出找车费用审批列表
     */
    @PreAuthorize("@ss.hasPermi('vw_car_order_examine:vw_car_order_examine:export')")
    @Log(title = "找车费用审批", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VwCarOrderExamine vwCarOrderExamine)
    {
        List<VwCarOrderExamine> list = vwCarOrderExamineService.selectVwCarOrderExamineList(vwCarOrderExamine);
        ExcelUtil<VwCarOrderExamine> util = new ExcelUtil<VwCarOrderExamine>(VwCarOrderExamine.class);
        util.exportExcel(response, list, "找车费用审批数据");
    }

    /**
     * 获取找车费用审批详细信息
     */
    @PreAuthorize("@ss.hasPermi('vw_car_order_examine:vw_car_order_examine:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(vwCarOrderExamineService.selectVwCarOrderExamineById(id));
    }

    /**
     * 新增找车费用审批
     */
    @PreAuthorize("@ss.hasPermi('vw_car_order_examine:vw_car_order_examine:add')")
    @Log(title = "找车费用审批", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VwCarOrderExamine vwCarOrderExamine)
    {
        return toAjax(vwCarOrderExamineService.insertVwCarOrderExamine(vwCarOrderExamine));
    }

    /**
     * 修改找车费用审批
     */
    @PreAuthorize("@ss.hasPermi('vw_car_order_examine:vw_car_order_examine:edit')")
    @Log(title = "找车费用审批", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VwCarOrderExamine vwCarOrderExamine)
    {
        return toAjax(vwCarOrderExamineService.updateVwCarOrderExamine(vwCarOrderExamine));
    }

    /**
     * 删除找车费用审批
     */
    @PreAuthorize("@ss.hasPermi('vw_car_order_examine:vw_car_order_examine:remove')")
    @Log(title = "找车费用审批", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(vwCarOrderExamineService.deleteVwCarOrderExamineByIds(ids));
    }
}
