<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="" prop="expenseAmount">
        <el-input
          v-model="queryParams.expenseAmount"
          placeholder="请输入费用金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="expenseDate">
        <el-date-picker clearable
          v-model="queryParams.expenseDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择费用发生日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="" prop="applicantId">
        <el-input
          v-model="queryParams.applicantId"
          placeholder="请输入申请人ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="applicantName">
        <el-input
          v-model="queryParams.applicantName"
          placeholder="请输入申请人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="applicationTime">
        <el-date-picker clearable
          v-model="queryParams.applicationTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择申请时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="" prop="approverId">
        <el-input
          v-model="queryParams.approverId"
          placeholder="请输入审批人ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="approverName">
        <el-input
          v-model="queryParams.approverName"
          placeholder="请输入审批人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="approvalTimeRange">
        <el-date-picker
          v-model="approvalTimeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          @change="handleApprovalTimeRangeChange"
          clearable>
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button
          :type="showPendingOnly ? 'success' : 'info'"
          icon="el-icon-s-check"
          size="mini"
          @click="togglePendingView">
          {{ showPendingOnly ? '显示全部' : '待我审批' }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">

      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['daily_expense_approval:daily_expense_approval:export']"
        >导出</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="daily_expense_approvalList">
      <el-table-column label="费用类型" align="center" prop="expenseType" />
      <el-table-column label="费用金额" align="center" prop="expenseAmount" />
      <el-table-column label="费用发生日期" align="center" prop="expenseDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expenseDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="费用说明" align="center" prop="expenseDescription" />
      <el-table-column label="申请人姓名" align="center" prop="applicantName" />
      <el-table-column label="申请时间" align="center" prop="applicationTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.applicationTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审批状态" align="center" prop="approvalStatus">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.approvalStatus)">
            {{ getStatusText(scope.row.approvalStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="审批人" align="center" prop="approverName" />
      <el-table-column label="审批时间" align="center" prop="approvalTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.approvalTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审批备注" align="center" prop="approvalRemark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-if="canApprove(scope.row)"
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleApprove(scope.row, 'approve')"
            v-hasPermi="['daily_expense_approval:daily_expense_approval:edit']"
          >通过</el-button>
          <el-button
            v-if="canApprove(scope.row)"
            size="mini"
            type="text"
            icon="el-icon-close"
            @click="handleApprove(scope.row, 'reject')"
            v-hasPermi="['daily_expense_approval:daily_expense_approval:edit']"
          >拒绝</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看详情弹窗 -->
    <el-dialog title="日常费用详情" :visible.sync="viewDialogVisible" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="费用类型">
          {{ getExpenseTypeLabel(viewData.expenseType) }}
        </el-descriptions-item>
        <el-descriptions-item label="费用金额">
          ￥{{ viewData.expenseAmount }}
        </el-descriptions-item>
        <el-descriptions-item label="费用发生日期">
          {{ parseTime(viewData.expenseDate, '{y}-{m}-{d}') }}
        </el-descriptions-item>
        <el-descriptions-item label="申请人">
          {{ viewData.applicantName }}
        </el-descriptions-item>
        <el-descriptions-item label="申请时间">
          {{ parseTime(viewData.applicationTime, '{y}-{m}-{d}') }}
        </el-descriptions-item>
        <el-descriptions-item label="审批状态">
          <el-tag :type="getStatusTagType(viewData.approvalStatus)">
            {{ getStatusText(viewData.approvalStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="审批人" v-if="viewData.approvalStatus !== '0'">
          {{ viewData.approverName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="审批时间" v-if="viewData.approvalStatus !== '0'">
          {{ viewData.approvalTime ? parseTime(viewData.approvalTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="费用说明" :span="2">
          {{ viewData.expenseDescription || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="审批备注" :span="2" v-if="viewData.approvalStatus !== '0'">
          {{ viewData.approvalRemark || '-' }}
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDaily_expense_approval, listPendingDaily_expense_approval, approveDaily_expense_approval } from "@/api/daily_expense_approval/daily_expense_approval"

export default {
  name: "Daily_expense_approval",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 日常花费审批表格数据
      daily_expense_approvalList: [],
      // 查看详情弹窗
      viewDialogVisible: false,
      viewData: {},
      // 审批时间范围
      approvalTimeRange: [],
      // 是否只显示待审批
      showPendingOnly: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        litigationCaseId: null,
        expenseType: null,
        expenseAmount: null,
        expenseDate: null,
        expenseDescription: null,
        receiptUrl: null,
        applicantId: null,
        applicantName: null,
        applicationTime: null,
        approvalStatus: null,
        approverId: null,
        approverName: null,
        approvalStartTime: null,
        approvalEndTime: null,
        approvalRemark: null,
      }
    }
  },
  created() {
    this.getList()
    // 检查是否从法诉页面跳转过来
    if (this.$route.query.litigationCaseId) {
      this.queryParams.litigationCaseId = this.$route.query.litigationCaseId
      this.handleQuery()
    }
  },
  methods: {
    /** 查询日常花费审批列表 */
    getList() {
      this.loading = true
      const apiMethod = this.showPendingOnly ? listPendingDaily_expense_approval : listDaily_expense_approval
      apiMethod(this.queryParams).then(response => {
        this.daily_expense_approvalList = response.rows
        this.total = response.total
        this.loading = false
      })
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.approvalTimeRange = []
      this.resetForm("queryForm")
      this.handleQuery()
    },

    /** 处理审批时间范围变化 */
    handleApprovalTimeRangeChange(dates) {
      if (dates && dates.length === 2) {
        this.queryParams.approvalStartTime = dates[0]
        this.queryParams.approvalEndTime = dates[1]
      } else {
        this.queryParams.approvalStartTime = null
        this.queryParams.approvalEndTime = null
      }
      this.handleQuery()
    },


    /** 导出按钮操作 */
    handleExport() {
      this.download('daily_expense_approval/daily_expense_approval/export', {
        ...this.queryParams
      }, `daily_expense_approval_${new Date().getTime()}.xlsx`)
    },

    // 表单重置
    reset() {
      this.form = {
        id: null,
        litigationCaseId: null,
        expenseType: null,
        expenseAmount: null,
        expenseDate: null,
        expenseDescription: null,
        receiptUrl: null,
        applicantId: null,
        applicantName: null,
        applicationTime: null,
        approvalStatus: '0', // 默认待审批
        approverId: null,
        approverName: null,
        approvalTime: null,
        approvalRemark: null,
        delFlag: '0' // 默认未删除
      }
      this.resetForm("form")
    },

    /** 审批操作 */
    handleApprove(row, action) {
      const statusText = action === 'approve' ? '通过' : '拒绝'

      if (action === 'approve') {
        // 通过审批，直接确认
        this.$confirm(`确认${statusText}该日常费用申请？`, `${statusText}审批`, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const approvalData = {
            action: action,
            remark: '审批通过'
          }

          approveDaily_expense_approval(row.id, approvalData).then(response => {
            this.$modal.msgSuccess(`${statusText}成功`)
            this.getList()
          }).catch(error => {
            this.$modal.msgError(`${statusText}失败：` + (error.msg || '未知错误'))
          })
        }).catch(() => {
          this.$modal.msgInfo('已取消审批')
        })
      } else {
        // 拒绝审批，需要填写理由
        this.$prompt(`请输入${statusText}理由`, `${statusText}审批`, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /.+/,
          inputErrorMessage: '请输入审批理由'
        }).then(({ value }) => {
          const approvalData = {
            action: action,
            remark: value
          }

          approveDaily_expense_approval(row.id, approvalData).then(response => {
            this.$modal.msgSuccess(`${statusText}成功`)
            this.getList()
          }).catch(error => {
            this.$modal.msgError(`${statusText}失败：` + (error.msg || '未知错误'))
          })
        }).catch(() => {
          this.$modal.msgInfo('已取消审批')
        })
      }
    },

    /** 查看详情 */
    handleView(row) {
      this.viewData = { ...row }
      this.viewDialogVisible = true
    },

    /** 获取费用类型标签 */
    getExpenseTypeLabel(type) {
      const typeMap = {
        'oil_fee': '油费',
        'road_fee': '路费',
        'meal_fee': '餐费',
        'accommodation_fee': '住宿费',
        'transport_fee': '交通费',
        'parking_fee': '停车费',
        'communication_fee': '通讯费',
        'other': '其他'
      }
      return typeMap[type] || type
    },

    /** 获取审批状态文本 */
    getStatusText(status) {
      const statusMap = {
        '0': '待审批',
        '1': '全部通过',
        '2': '已拒绝',
        '3': '主管审批中',
        '4': '总监审批中',
        '5': '财务主管审批中',
        '6': '总经理审批中'
      }
      return statusMap[status] || '未知状态'
    },

    /** 获取审批状态标签类型 */
    getStatusTagType(status) {
      const typeMap = {
        '0': 'warning',    // 待审批 - 橙色
        '1': 'success',    // 全部通过 - 绿色
        '2': 'danger',     // 已拒绝 - 红色
        '3': 'primary',    // 主管审批中 - 蓝色
        '4': 'primary',    // 总监审批中 - 蓝色
        '5': 'primary',    // 财务主管审批中 - 蓝色
        '6': 'primary'     // 总经理审批中 - 蓝色
      }
      return typeMap[status] || 'info'
    },

    /** 判断是否可以审批 */
    canApprove(row) {
      // 已完成的状态不能审批
      if (row.approvalStatus === '1' || row.approvalStatus === '2') {
        return false
      }

      // 这里可以根据当前用户角色和审批状态来判断
      // 简化处理：只要不是已完成状态就可以审批
      // 实际权限控制在后端进行
      return true
    },

    /** 切换待审批视图 */
    togglePendingView() {
      this.showPendingOnly = !this.showPendingOnly
      this.handleQuery()
    }
  }
}
</script>
